import axios from 'axios';

// Create axios instance with default configuration for Laravel Sanctum SPA
const api = axios.create({
  baseURL: 'http://localhost:8000/api',
  withCredentials: true, // Important for Sanctum SPA authentication
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'X-Requested-With': 'XMLHttpRequest', // Required by <PERSON><PERSON>
  },
  
});

// Request interceptor to handle CSRF token
api.interceptors.request.use(
  (config) => {
    // Get CSRF token from meta tag or cookie if available
    const token = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');
    if (token) {
      config.headers['X-CSRF-TOKEN'] = token;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle common errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle 401 Unauthorized
    if (error.response?.status === 401) {
      // Clear any stored auth state
      localStorage.removeItem('auth_user');
      // Redirect to login if not already there
      if (window.location.pathname !== '/login') {
        window.location.href = '/login';
      }
    }

    // Handle 419 CSRF token mismatch
    if (error.response?.status === 419) {
      // Refresh CSRF token and retry the request
      return api.get('/sanctum/csrf-cookie').then(() => {
        return api.request(error.config);
      });
    }

    return Promise.reject(error);
  }
);

export default api;

// Helper function to get CSRF cookie
export const getCsrfCookie = () => {
  return api.get('/sanctum/csrf-cookie');
};

// Helper function to handle API errors
export const handleApiError = (error: any): string => {
  if (error.response?.data?.message) {
    return error.response.data.message;
  }
  
  if (error.response?.data?.errors) {
    const errors = error.response.data.errors;
    const firstError = Object.values(errors)[0];
    if (Array.isArray(firstError)) {
      return firstError[0] as string;
    }
  }
  
  if (error.response?.status === 422) {
    return 'Validation failed. Please check your input.';
  }
  
  if (error.response?.status === 401) {
    return 'Authentication required. Please log in.';
  }
  
  if (error.response?.status === 403) {
    return 'Access denied. You do not have permission to perform this action.';
  }
  
  if (error.response?.status === 500) {
    return 'Server error. Please try again later.';
  }
  
  return error.message || 'An unexpected error occurred';
};
