<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;
use App\Http\Requests\Auth\LoginRequest;
use App\Http\Requests\Auth\RegisterRequest;
use App\Http\Resources\UserResource;
use App\Models\User;

class AuthController extends Controller
{
    /**
     * Get CSRF cookie for SPA authentication.
     */
    public function csrfCookie(): JsonResponse
    {
        return response()->json(['message' => 'CSRF cookie set']);
    }

    /**
     * Register a new user.
     */
    public function register(RegisterRequest $request): JsonResponse
    {
        $user = User::create([
            'name' => $request->validated('name'),
            'email' => $request->validated('email'),
            'password' => Hash::make($request->validated('password')),
            'status' => 'active',
        ]);

        Auth::login($user);

        return response()->json([
            'success' => true,
            'user' => new UserResource($user->load(['roles', 'permissions'])),
            'message' => 'Registration successful'
        ], 201);
    }

    /**
     * Login user.
     */
    public function login(LoginRequest $request): JsonResponse
    {
        $request->authenticate();

        $request->session()->regenerate();

        $user = Auth::user();
        $user->load(['roles', 'permissions']);

        return response()->json([
            'success' => true,
            'user' => new UserResource($user),
            'message' => 'Login successful'
        ]);
    }

    /**
     * Logout user.
     */
    public function logout(Request $request): JsonResponse
    {
        Auth::logout();
        $request->session()->invalidate();
        $request->session()->regenerateToken();

        return response()->json([
            'success' => true,
            'message' => 'Logout successful'
        ]);
    }

    /**
     * Get authenticated user.
     */
    public function user(Request $request): JsonResponse
    {
        $user = $request->user();
        if ($user) {
            $user->load(['roles', 'permissions']);
        }
        return response()->json([
            'success' => true,
            'user' => $user ? new UserResource($user) : null
        ]);
    }
}
