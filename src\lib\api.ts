import api from "./axios";

// Types
export interface User {
  id: number;
  name: string;
  email: string;
  status: string;
  created_at: string;
  updated_at: string;
  roles?: Role[];
  permissions?: Permission[];
}

export interface Role {
  id: number;
  name: string;
  description?: string;
  created_at: string;
  updated_at: string;
  permissions?: Permission[];
  users_count?: number;
}

export interface Permission {
  id: number;
  name: string;
  display_name: string;
  description?: string;
  category: string;
  created_at: string;
  updated_at: string;
  roles_count?: number;
  users_count?: number;
}

export interface UserActivity {
  id: number;
  user_id: number;
  action: string;
  description?: string;
  status: "success" | "failed" | "warning";
  ip_address?: string;
  user_agent?: string;
  created_at: string;
  user?: User;
}

export interface ApiResponse<T> {
  data: T;
  message?: string;
  errors?: Record<string, string[]>;
}

export interface PaginatedResponse<T> {
  data: T[];
  current_page: number;
  last_page: number;
  per_page: number;
  total: number;
}

// API Client using Axios
class ApiClient {
  private async request<T>(
    method: 'GET' | 'POST' | 'PUT' | 'DELETE',
    endpoint: string,
    data?: any
  ): Promise<ApiResponse<T>> {
    try {
      const response = await api.request({
        method,
        url: `/api${endpoint}`,
        data,
      });
      return response.data;
    } catch (error: any) {
      throw error;
    }
  }

  // User endpoints
  async getUsers(params?: {
    search?: string;
    role?: string;
    page?: number;
  }): Promise<ApiResponse<User[]>> {
    const searchParams = new URLSearchParams();
    if (params?.search) searchParams.append("search", params.search);
    if (params?.role) searchParams.append("role", params.role);
    if (params?.page) searchParams.append("page", params.page.toString());

    const query = searchParams.toString();
    return this.request<User[]>('GET', `/users${query ? `?${query}` : ""}`);
  }

  async getUser(id: number): Promise<ApiResponse<User>> {
    return this.request<User>('GET', `/users/${id}`);
  }

  async createUser(data: {
    name: string;
    email: string;
    password: string;
    role_ids?: number[];
  }): Promise<ApiResponse<User>> {
    return this.request<User>('POST', "/users", data);
  }

  async updateUser(
    id: number,
    data: {
      name: string;
      email: string;
      role_ids?: number[];
    },
  ): Promise<ApiResponse<User>> {
    return this.request<User>('PUT', `/users/${id}`, data);
  }

  async deleteUser(id: number): Promise<ApiResponse<void>> {
        return this.request<void>('DELETE', `/users/${id}`);
  }

  // Role endpoints
  async getRoles(params?: {
    search?: string;
    page?: number;
  }): Promise<ApiResponse<Role[]>> {
    const searchParams = new URLSearchParams();
    if (params?.search) searchParams.append("search", params.search);
    if (params?.page) searchParams.append("page", params.page.toString());

    const query = searchParams.toString();
    return this.request<Role[]>('GET', `/roles${query ? `?${query}` : ""}`);
  }

  async getRole(id: number): Promise<ApiResponse<Role>> {
    return this.request<Role>('GET', `/roles/${id}`);
  }

  async createRole(data: {
    name: string;
    description: string;
    permission_ids?: number[];
  }): Promise<ApiResponse<Role>> {
    return this.request<Role>('POST', "/roles", data);
  }

  async updateRole(
    id: number,
    data: {
      name: string;
      description: string;
      permission_ids?: number[];
    },
  ): Promise<ApiResponse<Role>> {
    return this.request<Role>('PUT', `/roles/${id}`, data);
  }

  async deleteRole(id: number): Promise<ApiResponse<void>> {
    return this.request<void>('DELETE', `/roles/${id}`);
  }

  // Permission endpoints
  async getPermissions(params?: {
    search?: string;
    category?: string;
    page?: number;
  }): Promise<ApiResponse<Permission[]>> {
    const searchParams = new URLSearchParams();
    if (params?.search) searchParams.append("search", params.search);
    if (params?.category) searchParams.append("category", params.category);
    if (params?.page) searchParams.append("page", params.page.toString());

    const query = searchParams.toString();
    return this.request<Permission[]>('GET',
      `/permissions${query ? `?${query}` : ""}`,
    );
  }

  async getPermissionsGrouped(): Promise<
    ApiResponse<Record<string, Permission[]>>
  > {
    return this.request<Record<string, Permission[]>>('GET', "/permissions/grouped");
  }

  async createPermission(data: {
    name: string;
    display_name: string;
    description?: string;
    category: string;
  }): Promise<ApiResponse<Permission>> {
    return this.request<Permission>('POST', "/permissions", data);
  }

  async updatePermission(
    id: number,
    data: {
      name: string;
      display_name: string;
      description?: string;
      category: string;
    },
  ): Promise<ApiResponse<Permission>> {
    return this.request<Permission>('PUT', `/permissions/${id}`, data);
  }

  async deletePermission(id: number): Promise<ApiResponse<void>> {
    return this.request<void>('DELETE', `/permissions/${id}`);
  }

  // User Activity endpoints
  async getUserActivities(params?: {
    search?: string;
    user_id?: number;
    action?: string;
    status?: string;
    date_from?: string;
    date_to?: string;
    page?: number;
  }): Promise<ApiResponse<UserActivity[]>> {
    const searchParams = new URLSearchParams();
    if (params?.search) searchParams.append("search", params.search);
    if (params?.user_id)
      searchParams.append("user_id", params.user_id.toString());
    if (params?.action) searchParams.append("action", params.action);
    if (params?.status) searchParams.append("status", params.status);
    if (params?.date_from) searchParams.append("date_from", params.date_from);
    if (params?.date_to) searchParams.append("date_to", params.date_to);
    if (params?.page) searchParams.append("page", params.page.toString());

    const query = searchParams.toString();
    return this.request<UserActivity[]>('GET',
      `/user-activities${query ? `?${query}` : ""}`,
    );
  }

  async getUserActivityStatistics(params?: {
    date_from?: string;
    date_to?: string;
  }): Promise<ApiResponse<any>> {
    const searchParams = new URLSearchParams();
    if (params?.date_from) searchParams.append("date_from", params.date_from);
    if (params?.date_to) searchParams.append("date_to", params.date_to);

    const query = searchParams.toString();
    return this.request<any>('GET',
      `/user-activities/statistics${query ? `?${query}` : ""}`,
    );
  }

  // Role-Permission assignment
  async assignPermissionsToRole(
    roleId: number,
    permissionIds: number[],
  ): Promise<ApiResponse<void>> {
    return this.request<void>('POST', `/roles/${roleId}/permissions`, { permission_ids: permissionIds });
  }

  // User-Permission assignment
  async assignPermissionsToUser(
    userId: number,
    permissionIds: number[],
  ): Promise<ApiResponse<void>> {
    return this.request<void>('POST', `/users/${userId}/permissions`, { permission_ids: permissionIds });
  }

  // User-Role assignment
  async assignRolesToUser(
    userId: number,
    roleIds: number[],
  ): Promise<ApiResponse<void>> {
    return this.request<void>('POST', `/users/${userId}/roles`, { role_ids: roleIds });
  }
}

// Create API client instance
export const userApi = new ApiClient();

// Re-export error handler from axios
export { handleApiError } from "./axios";
