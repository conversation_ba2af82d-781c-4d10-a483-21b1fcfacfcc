<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WidgetResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name' => $this->name,
            'description' => $this->description,
            'template' => $this->template,
            'primary_color' => $this->primary_color,
            'position' => $this->position,
            'welcome_message' => $this->welcome_message,
            'placeholder' => $this->placeholder,
            'bot_name' => $this->bot_name,
            'bot_avatar' => $this->bot_avatar,
            'auto_open' => $this->auto_open,
            'widget_theme' => $this->widget_theme,
            'widget_width' => $this->widget_width,
            'widget_height' => $this->widget_height,
            'auto_trigger' => $this->auto_trigger,
            'ai_model' => $this->ai_model,
            'knowledge_base' => $this->knowledge_base,
            'status' => $this->status,
            'is_active' => $this->is_active,
            'embed_code' => $this->embed_code,
            'stats' => $this->stats,
            'deployment_ready' => $this->isReadyForDeployment(),
            'creator' => $this->whenLoaded('creator', function () {
                return [
                    'id' => $this->creator->id,
                    'name' => $this->creator->name,
                    'email' => $this->creator->email
                ];
            }),
            'updater' => $this->whenLoaded('updater', function () {
                return [
                    'id' => $this->updater->id,
                    'name' => $this->updater->name,
                    'email' => $this->updater->email
                ];
            }),
            'created_at' => $this->created_at?->toISOString(),
            'updated_at' => $this->updated_at?->toISOString(),
        ];
    }
}
