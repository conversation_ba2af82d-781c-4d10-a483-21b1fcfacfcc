import api, { getCsrfCookie, handleApiError } from "@/lib/axios";

// Authentication types
export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterData {
  name: string;
  email: string;
  password: string;
  confirmPassword: string;
}

export interface AuthUser {
  id: number;
  name: string;
  email: string;
  status: string;
  email_verified_at?: string;
  roles?: Array<{
    id: number;
    name: string;
    description?: string;
  }>;
  permissions?: Array<{
    id: number;
    name: string;
    display_name: string;
    category: string;
  }>;
  created_at: string;
  updated_at: string;
}

export interface AuthResponse {
  success: boolean;
  user: AuthUser;
  message?: string;
}

// Authentication service class using Axios
// Authentication service class using Axios
class AuthService {
  // Get CSRF token for SPA authentication
  async getCsrfToken(): Promise<void> {
    await getCsrfCookie();
  }

  // Login user
  async login(credentials: LoginCredentials): Promise<AuthResponse> {
    // Get CSRF token first
    await this.getCsrfToken();

    const response = await api.post<AuthResponse>("/api/login", credentials);
    return response.data;
  }

  // Register user
  async register(
    userData: Omit<RegisterData, "confirmPassword">,
  ): Promise<AuthResponse> {
    // Get CSRF token first
    await this.getCsrfToken();

    const response = await api.post<AuthResponse>("/register", userData);
    return response.data;
  }

  // Logout user
  async logout(): Promise<void> {
    try {
      await api.post("/logout");
    } catch (error) {
      // Even if logout fails on server, clear local state
      console.warn("Logout request failed:", error);
    }
  }

  // Get current user
  async getCurrentUser(): Promise<AuthUser> {
    const response = await api.get<AuthResponse>("/user");
    return response.data.user;
  }

  // Refresh user data
  async refreshUser(): Promise<AuthUser> {
    return this.getCurrentUser();
  }

  // Check if user is authenticated
  async checkAuth(): Promise<boolean> {
    try {
      await this.getCurrentUser();
      return true;
    } catch (error) {
      return false;
    }
  }

  // Password reset request
  async requestPasswordReset(email: string): Promise<void> {
    await this.getCsrfToken();
    await api.post("/forgot-password", { email });
  }

  // Reset password
  async resetPassword(data: {
    token: string;
    email: string;
    password: string;
    password_confirmation: string;
  }): Promise<void> {
    await this.getCsrfToken();
    await api.post("/reset-password", data);
  }

  // Email verification
  async resendEmailVerification(): Promise<void> {
    await api.post("/email/verification-notification");
  }
}

// Create auth service instance
export const authService = new AuthService();

// Auth error handler
export const handleAuthError = (error: any): string => {
  return handleApiError(error);
};

// Auth validation helpers
export const authValidation = {
  isValidEmail: (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  },

  isStrongPassword: (password: string): boolean => {
    return (
      password.length >= 8 &&
      /[a-z]/.test(password) &&
      /[A-Z]/.test(password) &&
      /\d/.test(password)
    );
  },

  passwordsMatch: (password: string, confirmPassword: string): boolean => {
    return password === confirmPassword;
  },
};
